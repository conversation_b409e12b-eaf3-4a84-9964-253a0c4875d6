using System.Security.Cryptography;
using System.Text;
using Liam.Cryptography.Constants;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Interfaces;

namespace Liam.Cryptography.Services;

/// <summary>
/// SHA256哈希算法提供者实现
/// </summary>
public class Sha256HashProvider : IHashProvider
{
    /// <summary>
    /// 计算字符串的哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public string ComputeHash(string input)
    {
        if (input == null)
            throw new ArgumentNullException(nameof(input), "输入字符串不能为null");
        // 允许空字符串作为有效输入

        var data = Encoding.UTF8.GetBytes(input);
        return ComputeHash(data);
    }

    /// <summary>
    /// 计算字节数组的哈希值
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public string ComputeHash(byte[] input)
    {
        try
        {
            if (input == null)
                throw new ArgumentNullException(nameof(input), "输入数据不能为null");
            // 允许空字节数组作为有效输入

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(input);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new CryptographyException("哈希计算失败", ex);
        }
    }

    /// <summary>
    /// 计算字符串的哈希值（异步）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public async Task<string> ComputeHashAsync(string input, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => ComputeHash(input), cancellationToken);
    }

    /// <summary>
    /// 计算字节数组的哈希值（异步）
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public async Task<string> ComputeHashAsync(byte[] input, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => ComputeHash(input), cancellationToken);
    }

    /// <summary>
    /// 计算文件的哈希值
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public string ComputeFileHash(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException(CryptoConstants.ErrorMessages.FILE_NOT_FOUND, filePath);

            using var fileStream = File.OpenRead(filePath);
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(fileStream);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
        catch (Exception ex) when (!(ex is ArgumentException) && !(ex is FileNotFoundException))
        {
            throw new CryptographyException(CryptoConstants.ErrorMessages.FILE_ACCESS_FAILED, ex);
        }
    }

    /// <summary>
    /// 计算文件的哈希值（异步）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值（十六进制字符串）</returns>
    public async Task<string> ComputeFileHashAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException(CryptoConstants.ErrorMessages.FILE_NOT_FOUND, filePath);

            using var fileStream = File.OpenRead(filePath);
            using var sha256 = SHA256.Create();
            var hashBytes = await sha256.ComputeHashAsync(fileStream, cancellationToken);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
        catch (Exception ex) when (!(ex is ArgumentException) && !(ex is FileNotFoundException))
        {
            throw new CryptographyException(CryptoConstants.ErrorMessages.FILE_ACCESS_FAILED, ex);
        }
    }

    /// <summary>
    /// 验证哈希值
    /// </summary>
    /// <param name="input">输入数据</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    public bool VerifyHash(string input, string expectedHash)
    {
        try
        {
            if (string.IsNullOrEmpty(expectedHash))
                return false;

            var computedHash = ComputeHash(input);
            return string.Equals(computedHash, expectedHash.ToLowerInvariant(), StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证哈希值
    /// </summary>
    /// <param name="input">输入数据</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    public bool VerifyHash(byte[] input, string expectedHash)
    {
        try
        {
            if (string.IsNullOrEmpty(expectedHash))
                return false;

            var computedHash = ComputeHash(input);
            return string.Equals(computedHash, expectedHash.ToLowerInvariant(), StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }
}
