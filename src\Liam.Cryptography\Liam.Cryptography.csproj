﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>

    <!-- NuGet包信息 -->
    <PackageId>Liam.Cryptography</PackageId>
    <Version>1.0.1</Version>
    <Authors>Liam</Authors>
    <Description>Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能</Description>
    <PackageTags>cryptography;encryption;decryption;aes;rsa;sha256;md5;digital-signature;key-management;liam</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryUrl>https://gitee.com/your-username/Liam</RepositoryUrl>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
