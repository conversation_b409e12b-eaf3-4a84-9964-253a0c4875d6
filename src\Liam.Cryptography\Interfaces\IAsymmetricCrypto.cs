using Liam.Cryptography.Models;

namespace Liam.Cryptography.Interfaces;

/// <summary>
/// 非对称加密接口，提供非对称加密和解密功能
/// </summary>
public interface IAsymmetricCrypto
{
    /// <summary>
    /// 生成密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>密钥对</returns>
    KeyPair GenerateKeyPair(int keySize = 2048);

    /// <summary>
    /// 使用公钥加密数据
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密后的数据</returns>
    byte[] EncryptWithPublicKey(string plainText, string publicKey);

    /// <summary>
    /// 使用公钥加密数据（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的数据</returns>
    Task<byte[]> EncryptWithPublicKeyAsync(string plainText, string publicKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 使用私钥解密数据
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密后的明文</returns>
    string DecryptWithPrivateKey(byte[] cipherData, string privateKey);

    /// <summary>
    /// 使用私钥解密数据（异步）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    Task<string> DecryptWithPrivateKeyAsync(byte[] cipherData, string privateKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 使用私钥加密数据（用于数字签名）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>加密后的数据</returns>
    byte[] EncryptWithPrivateKey(string plainText, string privateKey);

    /// <summary>
    /// 使用公钥解密数据（用于验证数字签名）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>解密后的明文</returns>
    string DecryptWithPublicKey(byte[] cipherData, string publicKey);
}
