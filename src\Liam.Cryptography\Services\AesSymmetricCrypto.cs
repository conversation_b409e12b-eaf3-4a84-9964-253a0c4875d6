using System.Security.Cryptography;
using System.Text;
using Liam.Cryptography.Constants;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Interfaces;

namespace Liam.Cryptography.Services;

/// <summary>
/// AES对称加密服务实现
/// </summary>
public class AesSymmetricCrypto : ISymmetricCrypto
{
    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>加密后的数据</returns>
    public byte[] Encrypt(string plainText, byte[] key, byte[]? iv = null)
    {
        try
        {
            if (plainText == null)
                throw new ArgumentNullException(nameof(plainText), "明文不能为null");
            // 允许空字符串作为有效输入

            if (key == null)
                throw new ArgumentNullException(nameof(key), "密钥不能为空");
            if (key.Length == 0)
                throw new ArgumentException("密钥不能为空", nameof(key));

            using var aes = Aes.Create();
            aes.Key = key;
            aes.Mode = System.Security.Cryptography.CipherMode.CBC;
            aes.Padding = System.Security.Cryptography.PaddingMode.PKCS7;

            if (iv != null)
            {
                if (iv.Length != CryptoConstants.IVSizes.AES)
                    throw new ArgumentException($"AES初始化向量长度必须为{CryptoConstants.IVSizes.AES}字节", nameof(iv));
                aes.IV = iv;
            }
            else
            {
                aes.GenerateIV();
            }

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);

            swEncrypt.Write(plainText);
            swEncrypt.Close();

            var encrypted = msEncrypt.ToArray();

            // 如果没有提供IV，将IV和加密数据组合返回
            if (iv == null)
            {
                var result = new byte[aes.IV.Length + encrypted.Length];
                Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
                return result;
            }
            else
            {
                // 如果提供了IV，只返回加密数据
                return encrypted;
            }
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new EncryptionException(CryptoConstants.ErrorMessages.ENCRYPTION_FAILED, ex);
        }
    }

    /// <summary>
    /// 加密数据（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的数据</returns>
    public async Task<byte[]> EncryptAsync(string plainText, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Encrypt(plainText, key, iv), cancellationToken);
    }

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>解密后的明文</returns>
    public string Decrypt(byte[] cipherData, byte[] key, byte[]? iv = null)
    {
        try
        {
            if (cipherData == null)
                throw new ArgumentNullException(nameof(cipherData), "密文数据不能为空");
            if (cipherData.Length == 0)
                throw new ArgumentException("密文数据不能为空", nameof(cipherData));

            if (key == null)
                throw new ArgumentNullException(nameof(key), "密钥不能为空");
            if (key.Length == 0)
                throw new ArgumentException("密钥不能为空", nameof(key));

            using var aes = Aes.Create();
            aes.Key = key;
            aes.Mode = System.Security.Cryptography.CipherMode.CBC;
            aes.Padding = System.Security.Cryptography.PaddingMode.PKCS7;

            byte[] actualIV;
            byte[] actualCipherData;

            if (iv != null)
            {
                // 如果提供了IV，直接使用提供的IV和密文数据
                if (iv.Length != CryptoConstants.IVSizes.AES)
                    throw new ArgumentException($"AES初始化向量长度必须为{CryptoConstants.IVSizes.AES}字节", nameof(iv));
                actualIV = iv;
                actualCipherData = cipherData;
            }
            else
            {
                // 如果没有提供IV，从密文数据中提取IV
                if (cipherData.Length < CryptoConstants.IVSizes.AES)
                    throw new ArgumentException("密文数据长度不足", nameof(cipherData));

                actualIV = new byte[CryptoConstants.IVSizes.AES];
                actualCipherData = new byte[cipherData.Length - CryptoConstants.IVSizes.AES];

                Array.Copy(cipherData, 0, actualIV, 0, CryptoConstants.IVSizes.AES);
                Array.Copy(cipherData, CryptoConstants.IVSizes.AES, actualCipherData, 0, actualCipherData.Length);
            }

            aes.IV = actualIV;

            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(actualCipherData);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);

            return srDecrypt.ReadToEnd();
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new DecryptionException(CryptoConstants.ErrorMessages.DECRYPTION_FAILED, ex);
        }
    }

    /// <summary>
    /// 解密数据（异步）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    public async Task<string> DecryptAsync(byte[] cipherData, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => Decrypt(cipherData, key, iv), cancellationToken);
    }

    /// <summary>
    /// 生成随机密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>生成的密钥</returns>
    public byte[] GenerateKey(int keySize = 256)
    {
        if (keySize != 128 && keySize != 192 && keySize != 256)
            throw new ArgumentException("AES密钥长度必须为128、192或256位", nameof(keySize));

        using var aes = Aes.Create();
        aes.KeySize = keySize;
        aes.GenerateKey();
        return aes.Key;
    }

    /// <summary>
    /// 生成随机初始化向量
    /// </summary>
    /// <returns>生成的初始化向量</returns>
    public byte[] GenerateIV()
    {
        using var aes = Aes.Create();
        aes.GenerateIV();
        return aes.IV;
    }
}
