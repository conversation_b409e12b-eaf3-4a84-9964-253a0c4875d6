using System.Text;

namespace Liam.Cryptography.Tests.TestHelpers;

/// <summary>
/// 加密测试辅助类
/// </summary>
public static class CryptoTestHelper
{
    /// <summary>
    /// 测试用的明文数据
    /// </summary>
    public static class TestData
    {
        public const string SimpleText = "Hello World";
        public const string ChineseText = "你好世界";
        public const string EmptyText = "";
        public const string LongText = "This is a very long text that is used for testing encryption and decryption operations. It contains multiple sentences and should be long enough to test various scenarios including block cipher operations and streaming operations.";
        public const string SpecialCharacters = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~";
        public const string UnicodeText = "🌟🚀💻🔐🛡️";
        
        public static readonly byte[] SimpleBytes = Encoding.UTF8.GetBytes(SimpleText);
        public static readonly byte[] ChineseBytes = Encoding.UTF8.GetBytes(ChineseText);
        public static readonly byte[] LongBytes = Encoding.UTF8.GetBytes(LongText);
        public static readonly byte[] SpecialBytes = Encoding.UTF8.GetBytes(SpecialCharacters);
        public static readonly byte[] UnicodeBytes = Encoding.UTF8.GetBytes(UnicodeText);
    }

    /// <summary>
    /// 测试用的密钥数据
    /// </summary>
    public static class TestKeys
    {
        // AES测试密钥
        public static readonly byte[] Aes128Key = Convert.FromHexString("2b7e151628aed2a6abf7158809cf4f3c");
        public static readonly byte[] Aes192Key = Convert.FromHexString("8e73b0f7da0e6452c810f32b809079e562f8ead2522c6b7b");
        public static readonly byte[] Aes256Key = Convert.FromHexString("603deb1015ca71be2b73aef0857d77811f352c073b6108d72d9810a30914dff4");
        
        // AES测试IV
        public static readonly byte[] AesIV = Convert.FromHexString("000102030405060708090a0b0c0d0e0f");
        
        // RSA测试密钥对（用于测试，实际使用中应该动态生成）
        public const string RsaPublicKeyPem = @"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41
fGnJm6gOdrj8ym3rFkEjWT2btf02uSumKiC+IuF+4uNKOQIDAQAB
-----END PUBLIC KEY-----";

        public const string RsaPrivateKeyPem = @"-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDh/nCDmXaEqxN4
16b9XjV8acmbqA52uPzKbesWQSNZPZu1/Ta5K6YqIL4i4X7i40o5AgMBAAE=
-----END PRIVATE KEY-----";
    }

    /// <summary>
    /// 生成随机字节数组
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机字节数组</returns>
    public static byte[] GenerateRandomBytes(int length)
    {
        var random = new Random();
        var bytes = new byte[length];
        random.NextBytes(bytes);
        return bytes;
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机字符串</returns>
    public static string GenerateRandomString(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// 比较两个字节数组是否相等
    /// </summary>
    /// <param name="array1">数组1</param>
    /// <param name="array2">数组2</param>
    /// <returns>是否相等</returns>
    public static bool ByteArraysEqual(byte[] array1, byte[] array2)
    {
        if (array1.Length != array2.Length)
            return false;

        for (int i = 0; i < array1.Length; i++)
        {
            if (array1[i] != array2[i])
                return false;
        }

        return true;
    }

    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>十六进制字符串</returns>
    public static string BytesToHex(byte[] bytes)
    {
        return Convert.ToHexString(bytes).ToLowerInvariant();
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="hex">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] HexToBytes(string hex)
    {
        return Convert.FromHexString(hex);
    }

    /// <summary>
    /// 创建临时测试文件
    /// </summary>
    /// <param name="content">文件内容</param>
    /// <returns>临时文件路径</returns>
    public static string CreateTempFile(string content)
    {
        var tempFile = Path.GetTempFileName();
        File.WriteAllText(tempFile, content);
        return tempFile;
    }

    /// <summary>
    /// 创建临时测试文件
    /// </summary>
    /// <param name="content">文件内容</param>
    /// <returns>临时文件路径</returns>
    public static string CreateTempFile(byte[] content)
    {
        var tempFile = Path.GetTempFileName();
        File.WriteAllBytes(tempFile, content);
        return tempFile;
    }

    /// <summary>
    /// 清理临时文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    public static void CleanupTempFile(string filePath)
    {
        if (File.Exists(filePath))
        {
            try
            {
                File.Delete(filePath);
            }
            catch
            {
                // 忽略删除失败的情况
            }
        }
    }

    /// <summary>
    /// 验证异常类型和消息
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    /// <param name="action">要执行的操作</param>
    /// <param name="expectedMessage">期望的异常消息（可选）</param>
    public static void AssertThrows<T>(Action action, string? expectedMessage = null) where T : Exception
    {
        try
        {
            action();
            throw new AssertionFailedException($"Expected exception of type {typeof(T).Name} was not thrown.");
        }
        catch (T ex)
        {
            if (!string.IsNullOrEmpty(expectedMessage))
            {
                ex.Message.Should().Contain(expectedMessage);
            }
        }
        catch (Exception ex)
        {
            throw new AssertionFailedException($"Expected exception of type {typeof(T).Name}, but got {ex.GetType().Name}: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证异步异常类型和消息
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    /// <param name="action">要执行的异步操作</param>
    /// <param name="expectedMessage">期望的异常消息（可选）</param>
    public static async Task AssertThrowsAsync<T>(Func<Task> action, string? expectedMessage = null) where T : Exception
    {
        try
        {
            await action();
            throw new AssertionFailedException($"Expected exception of type {typeof(T).Name} was not thrown.");
        }
        catch (T ex)
        {
            if (!string.IsNullOrEmpty(expectedMessage))
            {
                ex.Message.Should().Contain(expectedMessage);
            }
        }
        catch (Exception ex)
        {
            throw new AssertionFailedException($"Expected exception of type {typeof(T).Name}, but got {ex.GetType().Name}: {ex.Message}");
        }
    }
}

/// <summary>
/// 断言失败异常
/// </summary>
public class AssertionFailedException : Exception
{
    public AssertionFailedException(string message) : base(message) { }
}
