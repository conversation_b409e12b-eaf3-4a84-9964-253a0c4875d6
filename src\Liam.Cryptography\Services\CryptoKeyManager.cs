using System.Security.Cryptography;
using System.Text;
using Liam.Cryptography.Constants;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Interfaces;
using Liam.Cryptography.Models;

namespace Liam.Cryptography.Services;

/// <summary>
/// 密钥管理服务实现
/// </summary>
public class CryptoKeyManager : IKeyManager
{
    /// <summary>
    /// 生成对称密钥
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>生成的密钥</returns>
    public byte[] GenerateSymmetricKey(int keySize = 256)
    {
        try
        {
            // 验证密钥长度：必须是有效的AES密钥长度
            if (keySize != 128 && keySize != 192 && keySize != 256)
                throw new ArgumentException("对称密钥长度必须是128、192或256位", nameof(keySize));

            var keyBytes = new byte[keySize / 8];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(keyBytes);
            return keyBytes;
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new KeyManagementException(CryptoConstants.ErrorMessages.KEY_GENERATION_FAILED, ex);
        }
    }

    /// <summary>
    /// 生成非对称密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>密钥对</returns>
    public KeyPair GenerateAsymmetricKeyPair(int keySize = 2048)
    {
        try
        {
            if (keySize < CryptoConstants.KeySizes.RSA_MINIMUM || keySize > CryptoConstants.KeySizes.RSA_MAXIMUM)
                throw new ArgumentException($"RSA密钥长度必须在{CryptoConstants.KeySizes.RSA_MINIMUM}到{CryptoConstants.KeySizes.RSA_MAXIMUM}位之间", nameof(keySize));

            using var rsa = RSA.Create(keySize);
            
            var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
            var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());

            return new KeyPair(publicKey, privateKey, keySize, CryptoConstants.Algorithms.RSA);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new KeyManagementException(CryptoConstants.ErrorMessages.KEY_GENERATION_FAILED, ex);
        }
    }

    /// <summary>
    /// 导出密钥到文件
    /// </summary>
    /// <param name="key">密钥数据</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    public void ExportKey(byte[] key, string filePath, string? password = null)
    {
        try
        {
            if (key == null)
                throw new ArgumentNullException(nameof(key), "密钥数据不能为null");
            if (key.Length == 0)
                throw new ArgumentException("密钥数据不能为空", nameof(key));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var keyData = Convert.ToBase64String(key);
            
            if (!string.IsNullOrEmpty(password))
            {
                // 使用密码保护密钥（简单的XOR加密，实际应用中应使用更强的加密）
                var passwordBytes = Encoding.UTF8.GetBytes(password);
                var keyBytes = Encoding.UTF8.GetBytes(keyData);
                
                for (int i = 0; i < keyBytes.Length; i++)
                {
                    keyBytes[i] ^= passwordBytes[i % passwordBytes.Length];
                }
                
                keyData = Convert.ToBase64String(keyBytes);
            }

            File.WriteAllText(filePath, keyData);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new KeyManagementException(CryptoConstants.ErrorMessages.FILE_ACCESS_FAILED, ex);
        }
    }

    /// <summary>
    /// 导出密钥对到文件
    /// </summary>
    /// <param name="keyPair">密钥对</param>
    /// <param name="privateKeyPath">私钥文件路径</param>
    /// <param name="publicKeyPath">公钥文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    public void ExportKeyPair(KeyPair keyPair, string privateKeyPath, string publicKeyPath, string? password = null)
    {
        if (keyPair == null)
            throw new ArgumentNullException(nameof(keyPair), "密钥对不能为null");
        if (!keyPair.IsValid())
            throw new ArgumentException("密钥对无效", nameof(keyPair));

        // 导出私钥 - 支持PEM格式
        byte[] privateKeyBytes;
        if (keyPair.PrivateKey!.StartsWith("-----BEGIN"))
        {
            // PEM格式，直接保存文本
            File.WriteAllText(privateKeyPath, keyPair.PrivateKey);
        }
        else
        {
            // Base64格式，转换后保存
            privateKeyBytes = Convert.FromBase64String(keyPair.PrivateKey);
            ExportKey(privateKeyBytes, privateKeyPath, password);
        }

        // 导出公钥 - 支持PEM格式
        if (keyPair.PublicKey!.StartsWith("-----BEGIN"))
        {
            // PEM格式，直接保存文本
            File.WriteAllText(publicKeyPath, keyPair.PublicKey);
        }
        else
        {
            // Base64格式，转换后保存
            var publicKeyBytes = Convert.FromBase64String(keyPair.PublicKey);
            ExportKey(publicKeyBytes, publicKeyPath, null); // 公钥通常不需要密码保护
        }
    }

    /// <summary>
    /// 从文件导入密钥
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    /// <returns>密钥数据</returns>
    public byte[] ImportKey(string filePath, string? password = null)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException(CryptoConstants.ErrorMessages.FILE_NOT_FOUND, filePath);

            var keyData = File.ReadAllText(filePath);
            
            if (!string.IsNullOrEmpty(password))
            {
                // 使用密码解密密钥
                var passwordBytes = Encoding.UTF8.GetBytes(password);
                var encryptedKeyBytes = Convert.FromBase64String(keyData);
                
                for (int i = 0; i < encryptedKeyBytes.Length; i++)
                {
                    encryptedKeyBytes[i] ^= passwordBytes[i % passwordBytes.Length];
                }
                
                keyData = Encoding.UTF8.GetString(encryptedKeyBytes);
            }

            return Convert.FromBase64String(keyData);
        }
        catch (Exception ex) when (!(ex is ArgumentException) && !(ex is FileNotFoundException))
        {
            throw new KeyManagementException(CryptoConstants.ErrorMessages.FILE_ACCESS_FAILED, ex);
        }
    }

    /// <summary>
    /// 从文件导入私钥
    /// </summary>
    /// <param name="filePath">私钥文件路径</param>
    /// <param name="password">保护密码（可选）</param>
    /// <returns>私钥</returns>
    public string ImportPrivateKey(string filePath, string? password = null)
    {
        var keyBytes = ImportKey(filePath, password);
        return Convert.ToBase64String(keyBytes);
    }

    /// <summary>
    /// 从文件导入公钥
    /// </summary>
    /// <param name="filePath">公钥文件路径</param>
    /// <returns>公钥</returns>
    public string ImportPublicKey(string filePath)
    {
        var keyBytes = ImportKey(filePath, null);
        return Convert.ToBase64String(keyBytes);
    }

    /// <summary>
    /// 验证密钥格式
    /// </summary>
    /// <param name="key">密钥</param>
    /// <param name="keyType">密钥类型</param>
    /// <returns>验证结果</returns>
    public bool ValidateKey(string key, KeyType keyType)
    {
        try
        {
            if (string.IsNullOrEmpty(key))
                return false;

            switch (keyType)
            {
                case KeyType.AsymmetricPrivate:
                    using (var rsa = RSA.Create())
                    {
                        if (key.StartsWith("-----BEGIN"))
                        {
                            rsa.ImportFromPem(key);
                        }
                        else
                        {
                            rsa.ImportRSAPrivateKey(Convert.FromBase64String(key), out _);
                        }
                        return true;
                    }

                case KeyType.AsymmetricPublic:
                    using (var rsa = RSA.Create())
                    {
                        if (key.StartsWith("-----BEGIN"))
                        {
                            rsa.ImportFromPem(key);
                        }
                        else
                        {
                            rsa.ImportRSAPublicKey(Convert.FromBase64String(key), out _);
                        }
                        return true;
                    }

                case KeyType.Symmetric:
                    var keyBytes = Convert.FromBase64String(key);
                    return keyBytes.Length > 0 && keyBytes.Length % 8 == 0;

                default:
                    return false;
            }
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 从密钥对中提取公钥
    /// </summary>
    /// <param name="privateKey">私钥</param>
    /// <returns>公钥</returns>
    public string ExtractPublicKey(string privateKey)
    {
        try
        {
            if (string.IsNullOrEmpty(privateKey))
                throw new ArgumentException("私钥不能为空", nameof(privateKey));

            using var rsa = RSA.Create();

            // 支持PEM格式和Base64格式
            if (privateKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(privateKey);
                return rsa.ExportSubjectPublicKeyInfoPem();
            }
            else
            {
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
                var publicKeyBytes = rsa.ExportRSAPublicKey();
                return Convert.ToBase64String(publicKeyBytes);
            }
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new KeyManagementException("从私钥提取公钥失败", ex);
        }
    }
}
