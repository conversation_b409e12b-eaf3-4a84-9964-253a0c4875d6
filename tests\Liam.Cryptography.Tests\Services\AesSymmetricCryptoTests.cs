using Liam.Cryptography.Services;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Tests.Fixtures;
using Liam.Cryptography.Tests.TestHelpers;

namespace Liam.Cryptography.Tests.Services;

/// <summary>
/// AES对称加密服务测试
/// </summary>
[Collection("Crypto Tests")]
public class AesSymmetricCryptoTests
{
    private readonly CryptoTestFixture _fixture;
    private readonly AesSymmetricCrypto _aesService;

    public AesSymmetricCryptoTests(CryptoTestFixture fixture)
    {
        _fixture = fixture;
        _aesService = _fixture.AesService;
    }

    #region 密钥生成测试

    [Theory]
    [InlineData(128)]
    [InlineData(192)]
    [InlineData(256)]
    public void GenerateKey_ValidKeySize_ShouldReturnCorrectLength(int keySize)
    {
        // Act
        var key = _aesService.GenerateKey(keySize);

        // Assert
        key.Should().NotBeNull();
        key.Length.Should().Be(keySize / 8);
    }

    [Theory]
    [InlineData(64)]
    [InlineData(512)]
    [InlineData(0)]
    [InlineData(-1)]
    public void GenerateKey_InvalidKeySize_ShouldThrowException(int keySize)
    {
        // Act & Assert
        var action = () => _aesService.GenerateKey(keySize);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void GenerateIV_ShouldReturnCorrectLength()
    {
        // Act
        var iv = _aesService.GenerateIV();

        // Assert
        iv.Should().NotBeNull();
        iv.Length.Should().Be(16); // AES块大小为128位（16字节）
    }

    [Fact]
    public void GenerateIV_MultipleCalls_ShouldReturnDifferentValues()
    {
        // Act
        var iv1 = _aesService.GenerateIV();
        var iv2 = _aesService.GenerateIV();

        // Assert
        iv1.Should().NotEqual(iv2);
    }

    #endregion

    #region 加密解密测试

    [Fact]
    public void Encrypt_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
        encrypted.Should().NotEqual(System.Text.Encoding.UTF8.GetBytes(plainText));
    }

    [Fact]
    public void Decrypt_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData("")]
    [InlineData("Hello World")]
    [InlineData("你好世界")]
    [InlineData("!@#$%^&*()_+-=[]{}|;':\",./<>?`~")]
    [InlineData("🌟🚀💻🔐🛡️")]
    public void EncryptDecrypt_DifferentTexts_ShouldPreserveOriginal(string plainText)
    {
        // Arrange
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void Encrypt_LongText_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.LongText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void Encrypt_WithoutIV_ShouldGenerateRandomIV()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;

        // Act
        var encrypted1 = _aesService.Encrypt(plainText, key);
        var encrypted2 = _aesService.Encrypt(plainText, key);

        // Assert
        encrypted1.Should().NotEqual(encrypted2); // 不同的IV应该产生不同的密文
    }

    #endregion

    #region 异步操作测试

    [Fact]
    public async Task EncryptAsync_ValidInput_ShouldReturnEncryptedData()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = await _aesService.EncryptAsync(plainText, key, iv);

        // Assert
        encrypted.Should().NotBeNull();
        encrypted.Length.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task DecryptAsync_ValidInput_ShouldReturnOriginalText()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = await _aesService.EncryptAsync(plainText, key, iv);
        var decrypted = await _aesService.DecryptAsync(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public async Task EncryptDecryptAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;
        using var cts = new CancellationTokenSource();

        // Act & Assert
        var encrypted = await _aesService.EncryptAsync(plainText, key, iv, cts.Token);
        var decrypted = await _aesService.DecryptAsync(encrypted, key, iv, cts.Token);

        decrypted.Should().Be(plainText);
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void Encrypt_NullPlainText_ShouldThrowArgumentException()
    {
        // Arrange
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act & Assert
        var action = () => _aesService.Encrypt(null!, key, iv);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Encrypt_NullKey_ShouldThrowArgumentNullException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var iv = _fixture.TestAesIV;

        // Act & Assert
        var action = () => _aesService.Encrypt(plainText, null!, iv);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Encrypt_InvalidKeyLength_ShouldThrowCryptographyException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var invalidKey = new byte[15]; // 无效的密钥长度
        var iv = _fixture.TestAesIV;

        // Act & Assert
        var action = () => _aesService.Encrypt(plainText, invalidKey, iv);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void Decrypt_InvalidCipherData_ShouldThrowCryptographyException()
    {
        // Arrange
        var invalidCipherData = new byte[] { 1, 2, 3, 4, 5 };
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act & Assert
        var action = () => _aesService.Decrypt(invalidCipherData, key, iv);
        action.Should().Throw<CryptographyException>();
    }

    [Fact]
    public void Decrypt_WrongKey_ShouldThrowCryptographyException()
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key1 = _fixture.TestAesKey;
        var key2 = _aesService.GenerateKey(256);
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key1, iv);

        // Assert
        var action = () => _aesService.Decrypt(encrypted, key2, iv);
        action.Should().Throw<CryptographyException>();
    }

    #endregion

    #region 边界条件测试

    [Fact]
    public void EncryptDecrypt_EmptyString_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = string.Empty;
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Fact]
    public void EncryptDecrypt_SingleCharacter_ShouldWorkCorrectly()
    {
        // Arrange
        var plainText = "A";
        var key = _fixture.TestAesKey;
        var iv = _fixture.TestAesIV;

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    [Theory]
    [InlineData(128)]
    [InlineData(192)]
    [InlineData(256)]
    public void EncryptDecrypt_DifferentKeySizes_ShouldWorkCorrectly(int keySize)
    {
        // Arrange
        var plainText = CryptoTestHelper.TestData.SimpleText;
        var key = _aesService.GenerateKey(keySize);
        var iv = _aesService.GenerateIV();

        // Act
        var encrypted = _aesService.Encrypt(plainText, key, iv);
        var decrypted = _aesService.Decrypt(encrypted, key, iv);

        // Assert
        decrypted.Should().Be(plainText);
    }

    #endregion
}
