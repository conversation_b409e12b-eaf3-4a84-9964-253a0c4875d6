using System.Text;
using Liam.Cryptography.Services;

namespace Liam.Cryptography.Extensions;

/// <summary>
/// 字符串加密扩展方法
/// </summary>
public static class StringCryptoExtensions
{
    /// <summary>
    /// 计算字符串的SHA256哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>SHA256哈希值（十六进制字符串）</returns>
    public static string ToSha256Hash(this string input)
    {
        var hashProvider = new Sha256HashProvider();
        return hashProvider.ComputeHash(input);
    }

    /// <summary>
    /// 计算字符串的SHA256哈希值（异步）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>SHA256哈希值（十六进制字符串）</returns>
    public static async Task<string> ToSha256HashAsync(this string input, CancellationToken cancellationToken = default)
    {
        var hashProvider = new Sha256HashProvider();
        return await hashProvider.ComputeHashAsync(input, cancellationToken);
    }

    /// <summary>
    /// 使用AES加密字符串
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>加密后的Base64字符串</returns>
    public static string EncryptAes(this string plainText, byte[] key, byte[]? iv = null)
    {
        var aes = new AesSymmetricCrypto();
        var encryptedBytes = aes.Encrypt(plainText, key, iv);
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用AES加密字符串（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的Base64字符串</returns>
    public static async Task<string> EncryptAesAsync(this string plainText, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        var aes = new AesSymmetricCrypto();
        var encryptedBytes = await aes.EncryptAsync(plainText, key, iv, cancellationToken);
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用AES解密字符串
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <returns>解密后的明文</returns>
    public static string DecryptAes(this string cipherText, byte[] key, byte[]? iv = null)
    {
        var aes = new AesSymmetricCrypto();
        var cipherBytes = Convert.FromBase64String(cipherText);
        return aes.Decrypt(cipherBytes, key, iv);
    }

    /// <summary>
    /// 使用AES解密字符串（异步）
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="key">密钥</param>
    /// <param name="iv">初始化向量（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    public static async Task<string> DecryptAesAsync(this string cipherText, byte[] key, byte[]? iv = null, CancellationToken cancellationToken = default)
    {
        var aes = new AesSymmetricCrypto();
        var cipherBytes = Convert.FromBase64String(cipherText);
        return await aes.DecryptAsync(cipherBytes, key, iv, cancellationToken);
    }

    /// <summary>
    /// 使用RSA公钥加密字符串
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密后的Base64字符串</returns>
    public static string EncryptRsa(this string plainText, string publicKey)
    {
        var rsa = new RsaAsymmetricCrypto();
        var encryptedBytes = rsa.EncryptWithPublicKey(plainText, publicKey);
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用RSA公钥加密字符串（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的Base64字符串</returns>
    public static async Task<string> EncryptRsaAsync(this string plainText, string publicKey, CancellationToken cancellationToken = default)
    {
        var rsa = new RsaAsymmetricCrypto();
        var encryptedBytes = await rsa.EncryptWithPublicKeyAsync(plainText, publicKey, cancellationToken);
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用RSA私钥解密字符串
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密后的明文</returns>
    public static string DecryptRsa(this string cipherText, string privateKey)
    {
        var rsa = new RsaAsymmetricCrypto();
        var cipherBytes = Convert.FromBase64String(cipherText);
        return rsa.DecryptWithPrivateKey(cipherBytes, privateKey);
    }

    /// <summary>
    /// 使用RSA私钥解密字符串（异步）
    /// </summary>
    /// <param name="cipherText">密文（Base64字符串）</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    public static async Task<string> DecryptRsaAsync(this string cipherText, string privateKey, CancellationToken cancellationToken = default)
    {
        var rsa = new RsaAsymmetricCrypto();
        var cipherBytes = Convert.FromBase64String(cipherText);
        return await rsa.DecryptWithPrivateKeyAsync(cipherBytes, privateKey, cancellationToken);
    }

    /// <summary>
    /// 使用RSA私钥对字符串进行数字签名
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>数字签名（Base64字符串）</returns>
    public static string SignRsa(this string data, string privateKey)
    {
        var signature = new RsaDigitalSignature();
        var signatureBytes = signature.Sign(data, privateKey);
        return Convert.ToBase64String(signatureBytes);
    }

    /// <summary>
    /// 使用RSA私钥对字符串进行数字签名（异步）
    /// </summary>
    /// <param name="data">要签名的数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>数字签名（Base64字符串）</returns>
    public static async Task<string> SignRsaAsync(this string data, string privateKey, CancellationToken cancellationToken = default)
    {
        var signature = new RsaDigitalSignature();
        var signatureBytes = await signature.SignAsync(data, privateKey, cancellationToken);
        return Convert.ToBase64String(signatureBytes);
    }

    /// <summary>
    /// 使用RSA公钥验证字符串的数字签名
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signatureBase64">数字签名（Base64字符串）</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>验证结果</returns>
    public static bool VerifyRsaSignature(this string data, string signatureBase64, string publicKey)
    {
        // 参数验证 - 抛出异常而不是返回false
        if (data == null)
            throw new ArgumentException("数据不能为null", nameof(data));
        if (signatureBase64 == null)
            throw new ArgumentException("签名不能为null", nameof(signatureBase64));
        if (publicKey == null)
            throw new ArgumentException("公钥不能为null", nameof(publicKey));

        try
        {
            var signature = new RsaDigitalSignature();
            var signatureBytes = Convert.FromBase64String(signatureBase64);
            return signature.Verify(data, signatureBytes, publicKey);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            // 只捕获非参数异常
            return false;
        }
    }

    /// <summary>
    /// 使用RSA公钥验证字符串的数字签名（异步）
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="signatureBase64">数字签名（Base64字符串）</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public static async Task<bool> VerifyRsaSignatureAsync(this string data, string signatureBase64, string publicKey, CancellationToken cancellationToken = default)
    {
        var signature = new RsaDigitalSignature();
        var signatureBytes = Convert.FromBase64String(signatureBase64);
        return await signature.VerifyAsync(data, signatureBytes, publicKey, cancellationToken);
    }

    /// <summary>
    /// 验证字符串的SHA256哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="expectedHash">期望的哈希值</param>
    /// <returns>验证结果</returns>
    public static bool VerifySha256Hash(this string input, string expectedHash)
    {
        var hashProvider = new Sha256HashProvider();
        return hashProvider.VerifyHash(input, expectedHash);
    }
}
