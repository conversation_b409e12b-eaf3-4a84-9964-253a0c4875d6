namespace Liam.Cryptography.Exceptions;

/// <summary>
/// 加密库基础异常类
/// </summary>
public class CryptographyException : Exception
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public CryptographyException() : base()
    {
        ErrorCode = "CRYPTO_ERROR";
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public CryptographyException(string message) : base(message)
    {
        ErrorCode = "CRYPTO_ERROR";
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public CryptographyException(string message, string errorCode) : base(message)
    {
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public CryptographyException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "CRYPTO_ERROR";
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="innerException">内部异常</param>
    public CryptographyException(string message, string errorCode, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }
}

/// <summary>
/// 加密操作异常
/// </summary>
public class EncryptionException : CryptographyException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public EncryptionException() : base()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public EncryptionException(string message) : base(message, "ENCRYPTION_ERROR")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public EncryptionException(string message, Exception innerException) : base(message, "ENCRYPTION_ERROR", innerException)
    {
    }
}

/// <summary>
/// 解密操作异常
/// </summary>
public class DecryptionException : CryptographyException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public DecryptionException() : base()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public DecryptionException(string message) : base(message, "DECRYPTION_ERROR")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public DecryptionException(string message, Exception innerException) : base(message, "DECRYPTION_ERROR", innerException)
    {
    }
}

/// <summary>
/// 密钥管理异常
/// </summary>
public class KeyManagementException : CryptographyException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public KeyManagementException() : base()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public KeyManagementException(string message) : base(message, "KEY_MANAGEMENT_ERROR")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public KeyManagementException(string message, Exception innerException) : base(message, "KEY_MANAGEMENT_ERROR", innerException)
    {
    }
}

/// <summary>
/// 数字签名异常
/// </summary>
public class DigitalSignatureException : CryptographyException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public DigitalSignatureException() : base()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    public DigitalSignatureException(string message) : base(message, "DIGITAL_SIGNATURE_ERROR")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    public DigitalSignatureException(string message, Exception innerException) : base(message, "DIGITAL_SIGNATURE_ERROR", innerException)
    {
    }
}
