<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Liam.Cryptography</name>
    </assembly>
    <members>
        <member name="T:<PERSON>.Cryptography.Constants.CryptoConstants">
            <summary>
            加密相关常量
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Constants.CryptoConstants.KeySizes">
            <summary>
            默认密钥长度
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.KeySizes.AES_DEFAULT">
            <summary>
            AES默认密钥长度（位）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.KeySizes.RSA_DEFAULT">
            <summary>
            RSA默认密钥长度（位）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.KeySizes.RSA_MINIMUM">
            <summary>
            RSA最小密钥长度（位）
            </summary>
        </member>
        <member name="F:<PERSON>.Cryptography.Constants.CryptoConstants.KeySizes.RSA_MAXIMUM">
            <summary>
            RSA最大密钥长度（位）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.KeySizes.DES">
            <summary>
            DES密钥长度（位）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.KeySizes.TRIPLE_DES">
            <summary>
            3DES密钥长度（位）
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Constants.CryptoConstants.IVSizes">
            <summary>
            初始化向量长度
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.IVSizes.AES">
            <summary>
            AES初始化向量长度（字节）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.IVSizes.DES">
            <summary>
            DES初始化向量长度（字节）
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.IVSizes.TRIPLE_DES">
            <summary>
            3DES初始化向量长度（字节）
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Constants.CryptoConstants.Algorithms">
            <summary>
            算法名称
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.AES">
            <summary>
            AES算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.DES">
            <summary>
            DES算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.TRIPLE_DES">
            <summary>
            3DES算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.RSA">
            <summary>
            RSA算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.MD5">
            <summary>
            MD5算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.SHA1">
            <summary>
            SHA1算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.SHA256">
            <summary>
            SHA256算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.SHA384">
            <summary>
            SHA384算法
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.Algorithms.SHA512">
            <summary>
            SHA512算法
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.INVALID_KEY_SIZE">
            <summary>
            无效的密钥长度
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.INVALID_KEY_FORMAT">
            <summary>
            无效的密钥格式
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.ENCRYPTION_FAILED">
            <summary>
            加密失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.DECRYPTION_FAILED">
            <summary>
            解密失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.SIGNING_FAILED">
            <summary>
            签名失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.VERIFICATION_FAILED">
            <summary>
            验证失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.KEY_GENERATION_FAILED">
            <summary>
            密钥生成失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.FILE_NOT_FOUND">
            <summary>
            文件不存在
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.FILE_ACCESS_FAILED">
            <summary>
            文件访问失败
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.ErrorMessages.UNSUPPORTED_ALGORITHM">
            <summary>
            不支持的算法
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Constants.CryptoConstants.FileExtensions">
            <summary>
            文件扩展名
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.FileExtensions.PRIVATE_KEY">
            <summary>
            私钥文件扩展名
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.FileExtensions.PUBLIC_KEY">
            <summary>
            公钥文件扩展名
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.FileExtensions.KEY">
            <summary>
            密钥文件扩展名
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Constants.CryptoConstants.FileExtensions.CERTIFICATE">
            <summary>
            证书文件扩展名
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Exceptions.CryptographyException">
            <summary>
            加密库基础异常类
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Exceptions.CryptographyException.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.CryptographyException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.CryptographyException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.CryptographyException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="errorCode">错误代码</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.CryptographyException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.CryptographyException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="errorCode">错误代码</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Cryptography.Exceptions.EncryptionException">
            <summary>
            加密操作异常
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.EncryptionException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.EncryptionException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.EncryptionException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Cryptography.Exceptions.DecryptionException">
            <summary>
            解密操作异常
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DecryptionException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DecryptionException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DecryptionException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Cryptography.Exceptions.KeyManagementException">
            <summary>
            密钥管理异常
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.KeyManagementException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.KeyManagementException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.KeyManagementException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Cryptography.Exceptions.DigitalSignatureException">
            <summary>
            数字签名异常
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DigitalSignatureException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DigitalSignatureException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Cryptography.Exceptions.DigitalSignatureException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions">
            <summary>
            字节数组加密扩展方法
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.ToSha256Hash(System.Byte[])">
            <summary>
            计算字节数组的SHA256哈希值
            </summary>
            <param name="input">输入字节数组</param>
            <returns>SHA256哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.ToSha256HashAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            计算字节数组的SHA256哈希值（异步）
            </summary>
            <param name="input">输入字节数组</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>SHA256哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.ToHexString(System.Byte[],System.Boolean)">
            <summary>
            将字节数组转换为十六进制字符串
            </summary>
            <param name="bytes">字节数组</param>
            <param name="lowercase">是否使用小写字母（默认为true）</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.ToBase64String(System.Byte[])">
            <summary>
            将字节数组转换为Base64字符串
            </summary>
            <param name="bytes">字节数组</param>
            <returns>Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.SecureClear(System.Byte[])">
            <summary>
            安全地清除字节数组内容
            </summary>
            <param name="bytes">要清除的字节数组</param>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.SecureEquals(System.Byte[],System.Byte[])">
            <summary>
            安全地比较两个字节数组是否相等（防止时序攻击）
            </summary>
            <param name="a">第一个字节数组</param>
            <param name="b">第二个字节数组</param>
            <returns>比较结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.GenerateRandomBytes(System.Int32)">
            <summary>
            生成随机字节数组
            </summary>
            <param name="length">字节数组长度</param>
            <returns>随机字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.VerifySha256Hash(System.Byte[],System.String)">
            <summary>
            验证字节数组的SHA256哈希值
            </summary>
            <param name="input">输入字节数组</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.EncryptAes(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            使用AES加密字节数组
            </summary>
            <param name="plainData">明文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>加密后的字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.EncryptAesAsync(System.Byte[],System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            使用AES加密字节数组（异步）
            </summary>
            <param name="plainData">明文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.DecryptAes(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            使用AES解密字节数组
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>解密后的字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.DecryptAesAsync(System.Byte[],System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            使用AES解密字节数组（异步）
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.SignRsa(System.Byte[],System.String)">
            <summary>
            使用RSA私钥对字节数组进行数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.SignRsaAsync(System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA私钥对字节数组进行数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.VerifyRsaSignature(System.Byte[],System.Byte[],System.String)">
            <summary>
            使用RSA公钥验证字节数组的数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.VerifyRsaSignatureAsync(System.Byte[],System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA公钥验证字节数组的数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.FromHexString(System.String)">
            <summary>
            将十六进制字符串转换为字节数组
            </summary>
            <param name="hex">十六进制字符串</param>
            <returns>字节数组</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.ByteArrayCryptoExtensions.FromBase64String(System.String)">
            <summary>
            将Base64字符串转换为字节数组
            </summary>
            <param name="base64">Base64字符串</param>
            <returns>字节数组</returns>
        </member>
        <member name="T:Liam.Cryptography.Extensions.StringCryptoExtensions">
            <summary>
            字符串加密扩展方法
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.ToSha256Hash(System.String)">
            <summary>
            计算字符串的SHA256哈希值
            </summary>
            <param name="input">输入字符串</param>
            <returns>SHA256哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.ToSha256HashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            计算字符串的SHA256哈希值（异步）
            </summary>
            <param name="input">输入字符串</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>SHA256哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.EncryptAes(System.String,System.Byte[],System.Byte[])">
            <summary>
            使用AES加密字符串
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>加密后的Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.EncryptAesAsync(System.String,System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            使用AES加密字符串（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.DecryptAes(System.String,System.Byte[],System.Byte[])">
            <summary>
            使用AES解密字符串
            </summary>
            <param name="cipherText">密文（Base64字符串）</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.DecryptAesAsync(System.String,System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            使用AES解密字符串（异步）
            </summary>
            <param name="cipherText">密文（Base64字符串）</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.EncryptRsa(System.String,System.String)">
            <summary>
            使用RSA公钥加密字符串
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <returns>加密后的Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.EncryptRsaAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA公钥加密字符串（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.DecryptRsa(System.String,System.String)">
            <summary>
            使用RSA私钥解密字符串
            </summary>
            <param name="cipherText">密文（Base64字符串）</param>
            <param name="privateKey">私钥</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.DecryptRsaAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA私钥解密字符串（异步）
            </summary>
            <param name="cipherText">密文（Base64字符串）</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.SignRsa(System.String,System.String)">
            <summary>
            使用RSA私钥对字符串进行数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名（Base64字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.SignRsaAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA私钥对字符串进行数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名（Base64字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.VerifyRsaSignature(System.String,System.String,System.String)">
            <summary>
            使用RSA公钥验证字符串的数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signatureBase64">数字签名（Base64字符串）</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.VerifyRsaSignatureAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用RSA公钥验证字符串的数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signatureBase64">数字签名（Base64字符串）</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Extensions.StringCryptoExtensions.VerifySha256Hash(System.String,System.String)">
            <summary>
            验证字符串的SHA256哈希值
            </summary>
            <param name="input">输入字符串</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:Liam.Cryptography.Interfaces.IAsymmetricCrypto">
            <summary>
            非对称加密接口，提供非对称加密和解密功能
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.GenerateKeyPair(System.Int32)">
            <summary>
            生成密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>密钥对</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.EncryptWithPublicKey(System.String,System.String)">
            <summary>
            使用公钥加密数据
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.EncryptWithPublicKeyAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用公钥加密数据（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.DecryptWithPrivateKey(System.Byte[],System.String)">
            <summary>
            使用私钥解密数据
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="privateKey">私钥</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.DecryptWithPrivateKeyAsync(System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            使用私钥解密数据（异步）
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IAsymmetricCrypto.EncryptWithPrivateKey(System.String,System.String)">
            <summary>
            使用私钥加密数据（用于数字签名）
            </summary>
            <param name="plainText">明文</param>
            <param name="privateKey">私钥</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="T:Liam.Cryptography.Interfaces.IDigitalSignature">
            <summary>
            数字签名接口
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.Sign(System.String,System.String)">
            <summary>
            生成数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.Sign(System.Byte[],System.String)">
            <summary>
            生成数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.SignAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            生成数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.SignAsync(System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            生成数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.Verify(System.String,System.Byte[],System.String)">
            <summary>
            验证数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.Verify(System.Byte[],System.Byte[],System.String)">
            <summary>
            验证数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.VerifyAsync(System.String,System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            验证数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IDigitalSignature.VerifyAsync(System.Byte[],System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            验证数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:Liam.Cryptography.Interfaces.IHashProvider">
            <summary>
            哈希算法提供者接口
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeHash(System.String)">
            <summary>
            计算字符串的哈希值
            </summary>
            <param name="input">输入字符串</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeHash(System.Byte[])">
            <summary>
            计算字节数组的哈希值
            </summary>
            <param name="input">输入字节数组</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            计算字符串的哈希值（异步）
            </summary>
            <param name="input">输入字符串</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeHashAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            计算字节数组的哈希值（异步）
            </summary>
            <param name="input">输入字节数组</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeFileHash(System.String)">
            <summary>
            计算文件的哈希值
            </summary>
            <param name="filePath">文件路径</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.ComputeFileHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            计算文件的哈希值（异步）
            </summary>
            <param name="filePath">文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.VerifyHash(System.String,System.String)">
            <summary>
            验证哈希值
            </summary>
            <param name="input">输入数据</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IHashProvider.VerifyHash(System.Byte[],System.String)">
            <summary>
            验证哈希值
            </summary>
            <param name="input">输入数据</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:Liam.Cryptography.Interfaces.IKeyManager">
            <summary>
            密钥管理接口
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.GenerateSymmetricKey(System.Int32)">
            <summary>
            生成对称密钥
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>生成的密钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.GenerateAsymmetricKeyPair(System.Int32)">
            <summary>
            生成非对称密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>密钥对</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ExportKey(System.Byte[],System.String,System.String)">
            <summary>
            导出密钥到文件
            </summary>
            <param name="key">密钥数据</param>
            <param name="filePath">文件路径</param>
            <param name="password">保护密码（可选）</param>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ExportKeyPair(Liam.Cryptography.Models.KeyPair,System.String,System.String,System.String)">
            <summary>
            导出密钥对到文件
            </summary>
            <param name="keyPair">密钥对</param>
            <param name="privateKeyPath">私钥文件路径</param>
            <param name="publicKeyPath">公钥文件路径</param>
            <param name="password">保护密码（可选）</param>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ImportKey(System.String,System.String)">
            <summary>
            从文件导入密钥
            </summary>
            <param name="filePath">文件路径</param>
            <param name="password">保护密码（可选）</param>
            <returns>密钥数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ImportPrivateKey(System.String,System.String)">
            <summary>
            从文件导入私钥
            </summary>
            <param name="filePath">私钥文件路径</param>
            <param name="password">保护密码（可选）</param>
            <returns>私钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ImportPublicKey(System.String)">
            <summary>
            从文件导入公钥
            </summary>
            <param name="filePath">公钥文件路径</param>
            <returns>公钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ValidateKey(System.String,Liam.Cryptography.Models.KeyType)">
            <summary>
            验证密钥格式
            </summary>
            <param name="key">密钥</param>
            <param name="keyType">密钥类型</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.IKeyManager.ExtractPublicKey(System.String)">
            <summary>
            从密钥对中提取公钥
            </summary>
            <param name="privateKey">私钥</param>
            <returns>公钥</returns>
        </member>
        <member name="T:Liam.Cryptography.Interfaces.ISymmetricCrypto">
            <summary>
            对称加密接口，提供对称加密和解密功能
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.Encrypt(System.String,System.Byte[],System.Byte[])">
            <summary>
            加密数据
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.EncryptAsync(System.String,System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            加密数据（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.Decrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            解密数据
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.DecryptAsync(System.Byte[],System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            解密数据（异步）
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.GenerateKey(System.Int32)">
            <summary>
            生成随机密钥
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>生成的密钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Interfaces.ISymmetricCrypto.GenerateIV">
            <summary>
            生成随机初始化向量
            </summary>
            <returns>生成的初始化向量</returns>
        </member>
        <member name="T:Liam.Cryptography.Models.KeyType">
            <summary>
            密钥类型枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.KeyType.Symmetric">
            <summary>
            对称密钥
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.KeyType.AsymmetricPublic">
            <summary>
            非对称公钥
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.KeyType.AsymmetricPrivate">
            <summary>
            非对称私钥
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.SymmetricAlgorithm">
            <summary>
            对称加密算法枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.SymmetricAlgorithm.AES128">
            <summary>
            AES-128
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.SymmetricAlgorithm.AES192">
            <summary>
            AES-192
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.SymmetricAlgorithm.AES256">
            <summary>
            AES-256
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.SymmetricAlgorithm.DES">
            <summary>
            DES
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.SymmetricAlgorithm.TripleDES">
            <summary>
            3DES
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.AsymmetricAlgorithm">
            <summary>
            非对称加密算法枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.AsymmetricAlgorithm.RSA">
            <summary>
            RSA
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.AsymmetricAlgorithm.DSA">
            <summary>
            DSA
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.AsymmetricAlgorithm.ECDSA">
            <summary>
            ECDSA
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.HashAlgorithm">
            <summary>
            哈希算法枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.HashAlgorithm.MD5">
            <summary>
            MD5
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.HashAlgorithm.SHA1">
            <summary>
            SHA1
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.HashAlgorithm.SHA256">
            <summary>
            SHA256
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.HashAlgorithm.SHA384">
            <summary>
            SHA384
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.HashAlgorithm.SHA512">
            <summary>
            SHA512
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.PaddingMode">
            <summary>
            填充模式枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.PaddingMode.PKCS7">
            <summary>
            PKCS7填充
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.PaddingMode.Zeros">
            <summary>
            零填充
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.PaddingMode.None">
            <summary>
            无填充
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.PaddingMode.ANSIX923">
            <summary>
            ANSIX923填充
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.PaddingMode.ISO10126">
            <summary>
            ISO10126填充
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.CipherMode">
            <summary>
            加密模式枚举
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.CipherMode.ECB">
            <summary>
            电子密码本模式
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.CipherMode.CBC">
            <summary>
            密码块链接模式
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.CipherMode.CFB">
            <summary>
            密码反馈模式
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.CipherMode.OFB">
            <summary>
            输出反馈模式
            </summary>
        </member>
        <member name="F:Liam.Cryptography.Models.CipherMode.CTR">
            <summary>
            计数器模式
            </summary>
        </member>
        <member name="T:Liam.Cryptography.Models.EncryptionResult">
            <summary>
            加密结果模型
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.Data">
            <summary>
            加密后的数据
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.IV">
            <summary>
            初始化向量（如果使用）
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.Salt">
            <summary>
            盐值（如果使用）
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.Algorithm">
            <summary>
            使用的算法
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.KeySize">
            <summary>
            密钥长度（位）
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.EncryptionResult.EncryptedAt">
            <summary>
            加密时间
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Models.EncryptionResult.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Models.EncryptionResult.#ctor(System.Byte[],System.String,System.Int32,System.Byte[],System.Byte[])">
            <summary>
            构造函数
            </summary>
            <param name="data">加密数据</param>
            <param name="algorithm">算法名称</param>
            <param name="keySize">密钥长度</param>
            <param name="iv">初始化向量</param>
            <param name="salt">盐值</param>
        </member>
        <member name="M:Liam.Cryptography.Models.EncryptionResult.ToBase64">
            <summary>
            获取Base64编码的加密数据
            </summary>
            <returns>Base64字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Models.EncryptionResult.ToHex">
            <summary>
            获取十六进制编码的加密数据
            </summary>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:Liam.Cryptography.Models.EncryptionResult.IsValid">
            <summary>
            验证结果是否有效
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:Liam.Cryptography.Models.KeyPair">
            <summary>
            密钥对模型
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.KeyPair.PublicKey">
            <summary>
            公钥
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.KeyPair.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.KeyPair.KeySize">
            <summary>
            密钥长度（位）
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.KeyPair.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Liam.Cryptography.Models.KeyPair.Algorithm">
            <summary>
            密钥算法类型
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Models.KeyPair.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Models.KeyPair.#ctor(System.String,System.String,System.Int32,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="publicKey">公钥</param>
            <param name="privateKey">私钥</param>
            <param name="keySize">密钥长度</param>
            <param name="algorithm">算法类型</param>
        </member>
        <member name="M:Liam.Cryptography.Models.KeyPair.IsValid">
            <summary>
            验证密钥对是否有效
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Models.KeyPair.ClearPrivateKey">
            <summary>
            清除私钥（安全考虑）
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Models.KeyPair.GetPublicKeyOnly">
            <summary>
            获取公钥信息
            </summary>
            <returns>仅包含公钥的密钥对</returns>
        </member>
        <member name="T:Liam.Cryptography.Services.AesSymmetricCrypto">
            <summary>
            AES对称加密服务实现
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.Encrypt(System.String,System.Byte[],System.Byte[])">
            <summary>
            加密数据
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.EncryptAsync(System.String,System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            加密数据（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.Decrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            解密数据
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.DecryptAsync(System.Byte[],System.Byte[],System.Byte[],System.Threading.CancellationToken)">
            <summary>
            解密数据（异步）
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="key">密钥</param>
            <param name="iv">初始化向量（可选）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.GenerateKey(System.Int32)">
            <summary>
            生成随机密钥
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>生成的密钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.AesSymmetricCrypto.GenerateIV">
            <summary>
            生成随机初始化向量
            </summary>
            <returns>生成的初始化向量</returns>
        </member>
        <member name="T:Liam.Cryptography.Services.CryptoKeyManager">
            <summary>
            密钥管理服务实现
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.GenerateSymmetricKey(System.Int32)">
            <summary>
            生成对称密钥
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>生成的密钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.GenerateAsymmetricKeyPair(System.Int32)">
            <summary>
            生成非对称密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>密钥对</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ExportKey(System.Byte[],System.String,System.String)">
            <summary>
            导出密钥到文件
            </summary>
            <param name="key">密钥数据</param>
            <param name="filePath">文件路径</param>
            <param name="password">保护密码（可选）</param>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ExportKeyPair(Liam.Cryptography.Models.KeyPair,System.String,System.String,System.String)">
            <summary>
            导出密钥对到文件
            </summary>
            <param name="keyPair">密钥对</param>
            <param name="privateKeyPath">私钥文件路径</param>
            <param name="publicKeyPath">公钥文件路径</param>
            <param name="password">保护密码（可选）</param>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ImportKey(System.String,System.String)">
            <summary>
            从文件导入密钥
            </summary>
            <param name="filePath">文件路径</param>
            <param name="password">保护密码（可选）</param>
            <returns>密钥数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ImportPrivateKey(System.String,System.String)">
            <summary>
            从文件导入私钥
            </summary>
            <param name="filePath">私钥文件路径</param>
            <param name="password">保护密码（可选）</param>
            <returns>私钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ImportPublicKey(System.String)">
            <summary>
            从文件导入公钥
            </summary>
            <param name="filePath">公钥文件路径</param>
            <returns>公钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ValidateKey(System.String,Liam.Cryptography.Models.KeyType)">
            <summary>
            验证密钥格式
            </summary>
            <param name="key">密钥</param>
            <param name="keyType">密钥类型</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.ExtractPublicKey(System.String)">
            <summary>
            从密钥对中提取公钥
            </summary>
            <param name="privateKey">私钥</param>
            <returns>公钥</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.EncryptKeyWithPassword(System.String,System.String)">
            <summary>
            使用PBKDF2 + AES-256-GCM加密密钥数据
            </summary>
            <param name="keyData">要加密的密钥数据</param>
            <param name="password">保护密码</param>
            <returns>加密后的数据（Base64格式）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.DecryptKeyWithPassword(System.String,System.String)">
            <summary>
            使用PBKDF2 + AES-256-GCM解密密钥数据
            </summary>
            <param name="encryptedData">加密的数据（Base64格式）</param>
            <param name="password">保护密码</param>
            <returns>解密后的密钥数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.CryptoKeyManager.DecryptKeyWithPasswordLegacy(System.String,System.String)">
            <summary>
            旧版XOR解密方式（向后兼容）
            </summary>
            <param name="encryptedData">加密的数据</param>
            <param name="password">保护密码</param>
            <returns>解密后的密钥数据</returns>
        </member>
        <member name="T:Liam.Cryptography.Services.RsaAsymmetricCrypto">
            <summary>
            RSA非对称加密服务实现
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.GenerateKeyPair(System.Int32)">
            <summary>
            生成密钥对
            </summary>
            <param name="keySize">密钥长度（位）</param>
            <returns>密钥对</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.EncryptWithPublicKey(System.String,System.String)">
            <summary>
            使用公钥加密数据
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.EncryptWithPublicKeyAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            使用公钥加密数据（异步）
            </summary>
            <param name="plainText">明文</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.DecryptWithPrivateKey(System.Byte[],System.String)">
            <summary>
            使用私钥解密数据
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="privateKey">私钥</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.DecryptWithPrivateKeyAsync(System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            使用私钥解密数据（异步）
            </summary>
            <param name="cipherData">密文数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>解密后的明文</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaAsymmetricCrypto.EncryptWithPrivateKey(System.String,System.String)">
            <summary>
            使用私钥加密数据（用于数字签名）
            </summary>
            <param name="plainText">明文</param>
            <param name="privateKey">私钥</param>
            <returns>加密后的数据</returns>
        </member>
        <member name="T:Liam.Cryptography.Services.RsaDigitalSignature">
            <summary>
            RSA数字签名服务实现
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.Sign(System.String,System.String)">
            <summary>
            生成数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.Sign(System.Byte[],System.String)">
            <summary>
            生成数字签名
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.SignAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            生成数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.SignAsync(System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            生成数字签名（异步）
            </summary>
            <param name="data">要签名的数据</param>
            <param name="privateKey">私钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>数字签名</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.Verify(System.String,System.Byte[],System.String)">
            <summary>
            验证数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.Verify(System.Byte[],System.Byte[],System.String)">
            <summary>
            验证数字签名
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.VerifyAsync(System.String,System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            验证数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.RsaDigitalSignature.VerifyAsync(System.Byte[],System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            验证数字签名（异步）
            </summary>
            <param name="data">原始数据</param>
            <param name="signature">数字签名</param>
            <param name="publicKey">公钥</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:Liam.Cryptography.Services.Sha256HashProvider">
            <summary>
            SHA256哈希算法提供者实现
            </summary>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeHash(System.String)">
            <summary>
            计算字符串的哈希值
            </summary>
            <param name="input">输入字符串</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeHash(System.Byte[])">
            <summary>
            计算字节数组的哈希值
            </summary>
            <param name="input">输入字节数组</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            计算字符串的哈希值（异步）
            </summary>
            <param name="input">输入字符串</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeHashAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            计算字节数组的哈希值（异步）
            </summary>
            <param name="input">输入字节数组</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeFileHash(System.String)">
            <summary>
            计算文件的哈希值
            </summary>
            <param name="filePath">文件路径</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.ComputeFileHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            计算文件的哈希值（异步）
            </summary>
            <param name="filePath">文件路径</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>哈希值（十六进制字符串）</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.VerifyHash(System.String,System.String)">
            <summary>
            验证哈希值
            </summary>
            <param name="input">输入数据</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Liam.Cryptography.Services.Sha256HashProvider.VerifyHash(System.Byte[],System.String)">
            <summary>
            验证哈希值
            </summary>
            <param name="input">输入数据</param>
            <param name="expectedHash">期望的哈希值</param>
            <returns>验证结果</returns>
        </member>
    </members>
</doc>
