# Liam.Cryptography

## 功能概述
Liam.Cryptography是Liam系列.NET功能库集合中的核心加密库，提供完整的加密解密功能，包括对称加密、非对称加密、哈希算法、数字签名和密钥管理等功能。

## 安装
```bash
dotnet add package Liam.Cryptography
```

## 快速开始
```csharp
using Liam.Cryptography.Services;
using Liam.Cryptography.Extensions;

// 使用AES对称加密
var aes = new AesSymmetricCrypto();
var key = aes.GenerateKey(256);
var encrypted = "Hello World".EncryptAes(key);
var decrypted = encrypted.DecryptAes(key);

// 使用RSA非对称加密
var rsa = new RsaAsymmetricCrypto();
var keyPair = rsa.GenerateKeyPair(2048);
var rsaEncrypted = "Hello World".EncryptRsa(keyPair.PublicKey);
var rsaDecrypted = rsaEncrypted.DecryptRsa(keyPair.PrivateKey);

// 计算SHA256哈希
var hash = "Hello World".ToSha256Hash();

// 数字签名
var signature = "Hello World".SignRsa(keyPair.PrivateKey);
var isValid = "Hello World".VerifyRsaSignature(signature, keyPair.PublicKey);
```

## API文档

### 核心接口
- **ISymmetricCrypto**: 对称加密接口
- **IAsymmetricCrypto**: 非对称加密接口
- **IHashProvider**: 哈希算法提供者接口
- **IDigitalSignature**: 数字签名接口
- **IKeyManager**: 密钥管理接口

### 主要类
- **AesSymmetricCrypto**: AES对称加密实现
- **RsaAsymmetricCrypto**: RSA非对称加密实现
- **Sha256HashProvider**: SHA256哈希算法实现
- **RsaDigitalSignature**: RSA数字签名实现
- **CryptoKeyManager**: 密钥管理服务实现

### 扩展方法
- **StringCryptoExtensions**: 字符串加密扩展方法
- **ByteArrayCryptoExtensions**: 字节数组加密扩展方法

## 使用示例

### 基础用法

#### AES对称加密
```csharp
var aes = new AesSymmetricCrypto();

// 生成密钥和IV
var key = aes.GenerateKey(256);
var iv = aes.GenerateIV();

// 加密
var plainText = "这是要加密的数据";
var encrypted = aes.Encrypt(plainText, key, iv);

// 解密
var decrypted = aes.Decrypt(encrypted, key, iv);
```

#### RSA非对称加密
```csharp
var rsa = new RsaAsymmetricCrypto();

// 生成密钥对
var keyPair = rsa.GenerateKeyPair(2048);

// 使用公钥加密
var plainText = "这是要加密的数据";
var encrypted = rsa.EncryptWithPublicKey(plainText, keyPair.PublicKey);

// 使用私钥解密
var decrypted = rsa.DecryptWithPrivateKey(encrypted, keyPair.PrivateKey);
```

#### SHA256哈希计算
```csharp
var hashProvider = new Sha256HashProvider();

// 计算字符串哈希
var hash1 = hashProvider.ComputeHash("Hello World");

// 计算文件哈希
var hash2 = hashProvider.ComputeFileHash("path/to/file.txt");

// 验证哈希
var isValid = hashProvider.VerifyHash("Hello World", hash1);
```

#### 数字签名
```csharp
var signature = new RsaDigitalSignature();
var keyPair = new RsaAsymmetricCrypto().GenerateKeyPair(2048);

// 生成签名
var data = "要签名的数据";
var sign = signature.Sign(data, keyPair.PrivateKey);

// 验证签名
var isValid = signature.Verify(data, sign, keyPair.PublicKey);
```

### 高级用法

#### 密钥管理
```csharp
var keyManager = new CryptoKeyManager();

// 生成对称密钥
var symmetricKey = keyManager.GenerateSymmetricKey(256);

// 生成非对称密钥对
var asymmetricKeyPair = keyManager.GenerateAsymmetricKeyPair(2048);

// 导出密钥到文件
keyManager.ExportKey(symmetricKey, "symmetric.key", "password");
keyManager.ExportKeyPair(asymmetricKeyPair, "private.pem", "public.pub", "password");

// 从文件导入密钥
var importedKey = keyManager.ImportKey("symmetric.key", "password");
var importedPrivateKey = keyManager.ImportPrivateKey("private.pem", "password");
var importedPublicKey = keyManager.ImportPublicKey("public.pub");
```

#### 异步操作
```csharp
// 异步加密
var encryptedAsync = await aes.EncryptAsync(plainText, key, iv);

// 异步解密
var decryptedAsync = await aes.DecryptAsync(encryptedAsync, key, iv);

// 异步哈希计算
var hashAsync = await hashProvider.ComputeHashAsync("Hello World");

// 异步数字签名
var signAsync = await signature.SignAsync(data, keyPair.PrivateKey);
var verifyAsync = await signature.VerifyAsync(data, signAsync, keyPair.PublicKey);
```

### 最佳实践

#### 1. 密钥安全管理
```csharp
// 使用安全的密钥长度
var key = aes.GenerateKey(256); // AES-256
var keyPair = rsa.GenerateKeyPair(2048); // RSA-2048

// 及时清除敏感数据
key.SecureClear();
keyPair.ClearPrivateKey();
```

#### 2. 异常处理
```csharp
try
{
    var encrypted = aes.Encrypt(plainText, key);
}
catch (EncryptionException ex)
{
    // 处理加密异常
    Console.WriteLine($"加密失败: {ex.Message}");
}
catch (CryptographyException ex)
{
    // 处理通用加密异常
    Console.WriteLine($"加密库异常: {ex.ErrorCode} - {ex.Message}");
}
```

#### 3. 使用扩展方法简化操作
```csharp
// 简化的哈希计算
var hash = "Hello World".ToSha256Hash();

// 简化的加密操作
var encrypted = "Hello World".EncryptAes(key);
var decrypted = encrypted.DecryptAes(key);

// 简化的数字签名
var signature = "Hello World".SignRsa(privateKey);
var isValid = "Hello World".VerifyRsaSignature(signature, publicKey);
```

## 支持的算法

### 对称加密
- AES-128/192/256
- DES（不推荐用于生产环境）
- 3DES（不推荐用于生产环境）

### 非对称加密
- RSA (1024-4096位)
- DSA
- ECDSA

### 哈希算法
- SHA256（推荐）
- SHA384
- SHA512
- SHA1（不推荐用于安全场景）
- MD5（不推荐用于安全场景）

### 数字签名
- RSA-SHA256
- DSA-SHA256
- ECDSA-SHA256

## 测试覆盖率

当前测试覆盖情况：
- **单元测试项目**: Liam.Cryptography.Tests
- **测试框架**: xUnit + Moq + FluentAssertions
- **测试用例总数**: 272个
- **测试通过率**: 100% (272/272个测试通过) 🎉
- **主要修复**:
  - ✅ AES加密解密核心问题已修复
  - ✅ 异常类型匹配问题已修复
  - ✅ 空字符串处理问题已修复
  - ✅ 字节数组扩展方法问题已修复
  - ✅ RSA密钥格式统一为PKCS#8标准格式
  - ✅ CryptoKeyManager异常处理优化
  - ✅ 对称密钥生成验证逻辑修复
  - ✅ 测试隔离问题修复
  - ✅ 数字签名验证异常处理优化
  - ✅ 字符串扩展方法异常处理修复
- **测试覆盖范围**:
  - ✅ AES对称加密服务测试 (AesSymmetricCryptoTests)
  - ✅ RSA非对称加密服务测试 (RsaAsymmetricCryptoTests)
  - ✅ SHA256哈希提供者测试 (Sha256HashProviderTests)
  - ✅ RSA数字签名服务测试 (RsaDigitalSignatureTests)
  - ✅ 密钥管理服务测试 (CryptoKeyManagerTests)
  - ✅ 密钥对模型测试 (KeyPairTests)
  - ✅ 字符串加密扩展方法测试 (StringCryptoExtensionsTests)
  - ✅ 字节数组加密扩展方法测试 (ByteArrayCryptoExtensionsTests)

### 测试运行命令
```bash
# 运行所有测试
dotnet test tests/Liam.Cryptography.Tests

# 运行特定测试类
dotnet test tests/Liam.Cryptography.Tests --filter "ClassName=AesSymmetricCryptoTests"

# 生成测试覆盖率报告
dotnet test tests/Liam.Cryptography.Tests --collect:"XPlat Code Coverage"
```

## 版本历史
| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| 1.0.0 | 2025-06-14 | 初始版本，包含完整的加密功能和单元测试 |

## 许可证
MIT License

## 贡献
欢迎提交Issue和Pull Request来改进这个库。

## 支持
如果您在使用过程中遇到问题，请在GitHub上提交Issue。
